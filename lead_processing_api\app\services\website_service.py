"""
Website Service for Lead Processing API.

Handles all website-related functionality including:
- Website analysis and scraping
- Business information extraction
- Website health checking
- Content analysis
"""

import os
import sys
from typing import Dict, Any, Optional

# Add the lead_processing directory to the path
lead_processing_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'lead_processing'))
if lead_processing_path not in sys.path:
    sys.path.insert(0, lead_processing_path)

# Import the website module
website_module = None
try:
    import website as website_module
    print("✅ Website Service: Successfully imported website module")
except ImportError as e:
    print(f"⚠️  Website Service: Could not import website module: {e}")


class WebsiteService:
    """Service for website analysis and scraping."""

    def __init__(self):
        """Initialize website service."""
        self.is_available = website_module is not None

        if not self.is_available:
            print("⚠️  Website service not available - website module not imported")

    def analyze_website(self, url: str) -> Dict[str, Any]:
        """
        Analyze a website and extract business information.

        Args:
            url: Website URL to analyze

        Returns:
            Dict containing analysis results
        """
        if not self.is_available:
            return {
                "error": "Website analysis service not available",
                "url": url,
                "business_info": None
            }

        try:
            # Use the existing website analysis functionality with timeout
            import threading
            import time

            result_container = {"result": None, "error": None}

            def run_analysis():
                try:
                    result_container["result"] = website_module.run(url)
                except Exception as e:
                    result_container["error"] = str(e)

            # Start analysis in a separate thread
            thread = threading.Thread(target=run_analysis)
            thread.daemon = True
            thread.start()

            # Wait for up to 30 seconds
            thread.join(timeout=30)

            if thread.is_alive():
                # Thread is still running, analysis timed out
                return {
                    "url": url,
                    "business_info": self._get_basic_website_info(url),
                    "analysis_successful": False,
                    "error": "Analysis timed out - returning basic information"
                }
            elif result_container["error"]:
                # Analysis completed but with error
                error_msg = result_container['error']

                # Check if it's a timeout error from the website module
                if "timed out" in error_msg.lower() or "timeout" in error_msg.lower():
                    return {
                        "url": url,
                        "business_info": self._get_basic_website_info(url),
                        "analysis_successful": False,
                        "error": "Website request timed out - returning basic information"
                    }
                else:
                    return {
                        "url": url,
                        "business_info": self._get_basic_website_info(url),
                        "analysis_successful": False,
                        "error": f"Analysis failed: {error_msg}"
                    }
            else:
                # Analysis completed successfully
                return {
                    "url": url,
                    "business_info": result_container["result"],
                    "analysis_successful": True,
                    "error": None
                }

        except Exception as e:
            return {
                "error": f"Website analysis failed: {str(e)}",
                "url": url,
                "business_info": None,
                "analysis_successful": False
            }

    def extract_business_info(self, url: str) -> Dict[str, Any]:
        """
        Extract specific business information from a website.

        Args:
            url: Website URL to analyze

        Returns:
            Dict containing business information
        """
        if not self.is_available:
            return {
                "domain": "Unavailable",
                "business_goal": "Unavailable",
                "description": "Website analysis service not available",
                "error": "Service not available"
            }

        try:
            # Use the existing business info extraction
            if hasattr(website_module, 'extract_business_info'):
                result = website_module.extract_business_info(url)
            else:
                # Fallback to general analysis
                result = website_module.run(url)

            # Normalize the result format
            if isinstance(result, dict):
                return {
                    "domain": result.get("domain", "Unknown"),
                    "business_goal": result.get("business_goal", "Unknown"),
                    "description": result.get("description", "No description available"),
                    "error": None
                }
            else:
                return {
                    "domain": "Unknown",
                    "business_goal": str(result) if result else "Unknown",
                    "description": "Analysis completed",
                    "error": None
                }

        except Exception as e:
            return {
                "domain": "Error",
                "business_goal": "Error",
                "description": f"Failed to analyze website: {str(e)}",
                "error": str(e)
            }

    def check_website_health(self, url: str) -> Dict[str, Any]:
        """
        Check if a website is accessible and responsive.

        Args:
            url: Website URL to check

        Returns:
            Dict containing health check results
        """
        try:
            import requests
            from urllib.parse import urlparse

            # Ensure URL has a scheme
            if not url.startswith(('http://', 'https://')):
                url = f"https://{url}"

            # Parse URL to get domain
            parsed = urlparse(url)
            domain = parsed.netloc or parsed.path

            # Make request with timeout
            response = requests.get(url, timeout=10, allow_redirects=True)

            return {
                "url": url,
                "domain": domain,
                "status_code": response.status_code,
                "response_time_ms": int(response.elapsed.total_seconds() * 1000),
                "accessible": response.status_code < 400,
                "redirected": len(response.history) > 0,
                "final_url": response.url,
                "error": None
            }

        except requests.exceptions.Timeout:
            return {
                "url": url,
                "domain": urlparse(url).netloc,
                "status_code": None,
                "response_time_ms": None,
                "accessible": False,
                "redirected": False,
                "final_url": None,
                "error": "Request timeout"
            }
        except requests.exceptions.ConnectionError:
            return {
                "url": url,
                "domain": urlparse(url).netloc,
                "status_code": None,
                "response_time_ms": None,
                "accessible": False,
                "redirected": False,
                "final_url": None,
                "error": "Connection error"
            }
        except Exception as e:
            return {
                "url": url,
                "domain": "Unknown",
                "status_code": None,
                "response_time_ms": None,
                "accessible": False,
                "redirected": False,
                "final_url": None,
                "error": str(e)
            }

    def get_website_metadata(self, url: str) -> Dict[str, Any]:
        """
        Extract metadata from a website (title, description, etc.).

        Args:
            url: Website URL to analyze

        Returns:
            Dict containing website metadata
        """
        try:
            import requests
            from bs4 import BeautifulSoup

            # Ensure URL has a scheme
            if not url.startswith(('http://', 'https://')):
                url = f"https://{url}"

            response = requests.get(url, timeout=5)
            soup = BeautifulSoup(response.content, 'html.parser')

            # Extract metadata
            title = soup.find('title')
            title_text = title.get_text().strip() if title else "No title"

            # Meta description
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            description = meta_desc.get('content', '').strip() if meta_desc else "No description"

            # Meta keywords
            meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
            keywords = meta_keywords.get('content', '').strip() if meta_keywords else "No keywords"

            return {
                "url": url,
                "title": title_text,
                "description": description,
                "keywords": keywords,
                "status_code": response.status_code,
                "error": None
            }

        except Exception as e:
            return {
                "url": url,
                "title": "Error",
                "description": "Error",
                "keywords": "Error",
                "status_code": None,
                "error": str(e)
            }

    def is_service_available(self) -> bool:
        """
        Check if the website service is available.

        Returns:
            bool: True if service is available, False otherwise
        """
        return self.is_available

    def _get_basic_website_info(self, url: str) -> Dict[str, Any]:
        """
        Get basic website information without AI analysis (fallback method).

        Args:
            url: Website URL to analyze

        Returns:
            Dict containing basic website information
        """
        try:
            from urllib.parse import urlparse

            # Normalize URL
            if not url.startswith(('http://', 'https://')):
                url = f"https://{url}"

            domain = urlparse(url).netloc.replace('www.', '')
            company_name = domain.split('.')[0].replace('-', ' ').title()

            # Get basic metadata with timeout handling
            try:
                metadata = self.get_website_metadata(url)
            except Exception:
                metadata = {"description": "Website metadata not available"}

            return {
                "company_name": company_name,
                "website": {
                    "main": url,
                    "about": None
                },
                "social_links": {},
                "business_goals": [f"Information extracted from {company_name} website"],
                "about_the_business": [metadata.get("description", "Business information not available")],
                "target_market": ["Target market information not available"],
                "products_services": ["Products and services information not available"],
                "unique_value_proposition": ["Unique value proposition not available"]
            }
        except Exception as e:
            return {
                "company_name": "Unknown",
                "website": {"main": url, "about": None},
                "social_links": {},
                "business_goals": ["Information not available"],
                "about_the_business": ["Information not available"],
                "target_market": ["Information not available"],
                "products_services": ["Information not available"],
                "unique_value_proposition": ["Information not available"]
            }

    def get_service_status(self) -> Dict[str, Any]:
        """
        Get detailed service status information.

        Returns:
            Dict containing service status
        """
        return {
            "service": "website_service",
            "available": self.is_available,
            "module_imported": website_module is not None,
            "capabilities": [
                "website_analysis",
                "business_info_extraction",
                "health_checking",
                "metadata_extraction"
            ] if self.is_available else [],
            "dependencies": {
                "website_module": website_module is not None,
                "requests": True,  # Always available
                "beautifulsoup4": True  # Assume available
            }
        }
