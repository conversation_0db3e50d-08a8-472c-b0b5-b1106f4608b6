from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, HttpUrl
from typing import Dict, List, Any, Optional
import sys
import os

from .. import auth, models

# Add the lead_processing directory to the path
lead_processing_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'lead_processing'))
if lead_processing_path not in sys.path:
    sys.path.insert(0, lead_processing_path)

# Import the website module
website_module = None
try:
    import website as website_module
    print("✅ Website route: Successfully imported website module")
except ImportError as e:
    print(f"⚠️  Website route: Could not import website module: {e}")

router = APIRouter(
    prefix="/website",
    tags=["website"],
)

# Pydantic models for website analysis
class WebsiteAnalysisRequest(BaseModel):
    """Request model for website analysis."""
    url: str

    class Config:
        json_schema_extra = {
            "example": {
                "url": "example-farm.com"
            }
        }

class SocialMediaLinks(BaseModel):
    """Social media links found on the website."""
    facebook: Optional[str] = None
    twitter: Optional[str] = None
    instagram: Optional[str] = None
    linkedin: Optional[str] = None
    youtube: Optional[str] = None
    tiktok: Optional[str] = None

class WebsiteInfo(BaseModel):
    """Website information structure."""
    main: str
    about: Optional[str] = None

class WebsiteAnalysisResponse(BaseModel):
    """Response model for website analysis."""
    company_name: str
    website: WebsiteInfo
    social_links: SocialMediaLinks
    business_goals: List[str]
    about_the_business: List[str]
    target_market: List[str]
    products_services: List[str]
    unique_value_proposition: List[str]

    class Config:
        json_schema_extra = {
            "example": {
                "company_name": "Example Farm",
                "website": {
                    "main": "https://example-farm.com",
                    "about": "https://example-farm.com/about"
                },
                "social_links": {
                    "facebook": "https://facebook.com/examplefarm",
                    "instagram": "https://instagram.com/examplefarm"
                },
                "business_goals": ["Promote organic farming", "Increase sustainability"],
                "about_the_business": ["Family-owned organic farm since 1985"],
                "target_market": ["Health-conscious consumers", "Local restaurants"],
                "products_services": ["Organic vegetables", "Farm-to-table delivery"],
                "unique_value_proposition": ["100% organic", "Local sourcing"]
            }
        }

class WebsiteError(BaseModel):
    """Error response for website analysis."""
    error: str

@router.post("/analyze", response_model=WebsiteAnalysisResponse)
def analyze_website(
    request: WebsiteAnalysisRequest,
    current_user: Optional[models.User] = Depends(auth.get_current_user_optional)
):
    """
    Analyze a website and extract business information.

    This endpoint:
    1. Scrapes the website content
    2. Extracts social media links
    3. Analyzes business information using AI
    4. Returns structured business data

    **Parameters:**
    - **url**: Website URL to analyze (with or without http/https)

    **Returns:**
    - Complete business analysis including goals, target market, and social media presence
    """
    if website_module is None:
        raise HTTPException(
            status_code=500,
            detail="Website analysis module not available"
        )

    try:
        # Use the existing website analysis function
        result = website_module.run(request.url)

        # Check if there was an error
        if isinstance(result, dict) and "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])

        # Convert social_links to the expected format
        social_links = SocialMediaLinks(
            facebook=result.get("social_links", {}).get("facebook"),
            twitter=result.get("social_links", {}).get("twitter"),
            instagram=result.get("social_links", {}).get("instagram"),
            linkedin=result.get("social_links", {}).get("linkedin"),
            youtube=result.get("social_links", {}).get("youtube"),
            tiktok=result.get("social_links", {}).get("tiktok")
        )

        # Convert website info
        website_info = WebsiteInfo(
            main=result.get("website", {}).get("main", request.url),
            about=result.get("website", {}).get("about")
        )

        # Create response
        response = WebsiteAnalysisResponse(
            company_name=result.get("company_name", "Unknown"),
            website=website_info,
            social_links=social_links,
            business_goals=result.get("business_goals", ["Information not available"]),
            about_the_business=result.get("about_the_business", ["Information not available"]),
            target_market=result.get("target_market", ["Information not available"]),
            products_services=result.get("products_services", ["Information not available"]),
            unique_value_proposition=result.get("unique_value_proposition", ["Information not available"])
        )

        return response

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Website analysis failed: {str(e)}"
        )

@router.get("/health")
def website_health_check():
    """Health check for website analysis functionality."""
    if website_module is None:
        return {"status": "unavailable", "message": "Website module not loaded"}
    return {"status": "available", "message": "Website analysis ready"}
