import streamlit as st
import os
import json
import pandas as pd
from dotenv import load_dotenv
import return_competitors
import post_collector
import time
from lead_processing.main import collect_social_media_posts
import website

# Load environment variables
load_dotenv()

# Set page configuration
st.set_page_config(
    page_title="Deylegen - Competitor Analyzer & Social Media Monitoring",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1E88E5;
        font-weight: bold;
        margin-bottom: 1rem;
    }
    .sub-header {
        font-size: 1.8rem;
        color: #0D47A1;
        font-weight: bold;
        margin-top: 1rem;
        margin-bottom: 0.5rem;
    }
    .section-header {
        font-size: 1.5rem;
        color: #1565C0;
        font-weight: bold;
        margin-top: 1rem;
    }
    .info-text {
        font-size: 1rem;
        color: #424242;
    }
    .highlight {
        background-color: #E3F2FD;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    .social-card {
        background-color: #F5F5F5;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 0.5rem;
    }
    .competitor-card {
        background-color: #E8F5E9;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        border-left: 4px solid #43A047;
    }
    .post-container {
        max-height: 400px;
        overflow-y: auto;
        padding-right: 10px;
    }
</style>
""", unsafe_allow_html=True)

# App title and description
st.markdown("<div class='main-header'>Deylengen - Competitor Analyzer & Social Media Monitoring</div>", unsafe_allow_html=True)
st.markdown("<div class='info-text'>Enter your website URL to automatically extract business information, analyze competitors, and monitor their social media activity. All information will be displayed on the interface.</div>", unsafe_allow_html=True)

# Initialize session state variables if they don't exist
if 'competitors' not in st.session_state:
    st.session_state.competitors = None
if 'social_media_data' not in st.session_state:
    st.session_state.social_media_data = {}
if 'business_info' not in st.session_state:
    st.session_state.business_info = None
if 'website_info' not in st.session_state:
    st.session_state.website_info = None

# Main content area
st.markdown("<div class='sub-header'>Business Analysis & Social Media Monitoring</div>", unsafe_allow_html=True)

# Input form for business URL
with st.form("business_details_form"):
    url = st.text_input("Your Website URL", placeholder="e.g., example.com")

    submit_button = st.form_submit_button("Analyze Business & Competitors")

    if submit_button:
        if not url:
            st.error("Please enter your website URL to proceed with the analysis.")
        else:
            with st.spinner("Analyzing your business and competitors... This may take a minute."):
                try:
                    # Initialize the analyzer
                    analyzer = return_competitors.CompetitorAnalyzer()

                    # Scrape website and extract detailed information
                    with st.status("Scraping website and extracting information...") as status:
                        # Get website information
                        st.session_state.website_info = website.run(url)

                        # Check if website_info is a dictionary before trying to access keys
                        if isinstance(st.session_state.website_info, dict):
                            if "error" in st.session_state.website_info:
                                st.warning(f"Website scraping issue: {st.session_state.website_info['error']}")
                                # Continue with the analysis even if website scraping had issues
                            else:
                                # Display a preview of the extracted information
                                st.write("Website scraped successfully!")

                                # Show a brief preview without using an expander
                                st.write("**Preview of extracted information:**")
                                if "company_name" in st.session_state.website_info:
                                    st.write(f"**Company Name:** {st.session_state.website_info['company_name']}")

                                if "business_goals" in st.session_state.website_info and st.session_state.website_info["business_goals"]:
                                    st.write("**Business Goals:** " + st.session_state.website_info["business_goals"][0] + "...")
                        else:
                            # Handle case where website_info is not a dictionary
                            st.warning("Website scraping returned unexpected data format. Continuing with analysis...")

                        status.update(label="Website information extracted successfully!", state="complete")

                    # Extract business info and get competitor information
                    with st.status("Extracting business information...") as status:
                        business_info = analyzer.extract_business_info(url)
                        # Store business info in session state
                        st.session_state.business_info = {
                            'url': url,
                            'domain': business_info['domain'],
                            'business_goal': business_info['business_goal']
                        }
                        st.write(f"**Domain/Industry:** {business_info['domain']}")
                        st.write(f"**Business Goal:** {business_info['business_goal']}")
                        status.update(label="Business information extracted successfully!", state="complete")

                    with st.status("Analyzing competitors...") as status:
                        st.session_state.competitors = analyzer.analyze_and_format(url)
                        status.update(label="Competitor analysis completed successfully!", state="complete")

                    # Display success message
                    st.success("Analysis completed successfully!")

                    # Automatically collect social media posts for all competitors
                    for competitor in st.session_state.competitors:
                        with st.spinner(f"Collecting social media posts for {competitor['name']}..."):
                            try:
                                social_media_data = collect_social_media_posts(competitor)
                                st.session_state.social_media_data[competitor['name']] = social_media_data
                            except Exception as e:
                                st.warning(f"Could not collect social media posts for {competitor['name']}: {str(e)}")

                except Exception as e:
                    st.error(f"An error occurred during analysis: {str(e)}")

# Display results if available
if st.session_state.competitors:
    # Create tabs for different sections
    results_tabs = st.tabs(["Business Information", "Competitor Overview", "Social Media Analysis"])

    with results_tabs[0]:
        st.markdown("<div class='section-header'>Business Information</div>", unsafe_allow_html=True)

        # Display business information
        if st.session_state.business_info is not None:
            business_info = st.session_state.business_info

            # Create a styled container for business info
            st.markdown("""
            <style>
            .business-info-container {
                background-color: #E8F5E9;
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 20px;
                border-left: 5px solid #43A047;
            }
            .section-title {
                font-size: 1.3rem;
                font-weight: bold;
                color: #2E7D32;
                margin-top: 15px;
                margin-bottom: 10px;
            }
            .subsection-title {
                font-size: 1.1rem;
                font-weight: bold;
                color: #388E3C;
                margin-top: 10px;
                margin-bottom: 5px;
            }
            .info-item {
                margin-left: 15px;
                margin-bottom: 5px;
            }
            </style>
            """, unsafe_allow_html=True)

            # All Business Information in one container
            st.markdown("<div class='business-info-container'>", unsafe_allow_html=True)

            # Display detailed website information if available
            if st.session_state.website_info and isinstance(st.session_state.website_info, dict) and "error" not in st.session_state.website_info:
                website_info = st.session_state.website_info

                # Add styles for the website.py format
                st.markdown("""
                <style>
                .section-header {
                    font-size: 1.5rem;
                    font-weight: bold;
                    color: #1E88E5;
                    margin-top: 25px;
                    margin-bottom: 15px;
                }
                .emoji-header {
                    font-size: 1.3rem;
                    font-weight: bold;
                    margin-top: 20px;
                    margin-bottom: 10px;
                    color: #0D47A1;
                }
                .info-item {
                    margin-left: 20px;
                    margin-bottom: 5px;
                }
                </style>
                """, unsafe_allow_html=True)

                # Company Name
                if "company_name" in website_info:
                    st.markdown(f"<div class='section-header'>BUSINESS INFORMATION FOR: {website_info['company_name'].upper()}</div>", unsafe_allow_html=True)

                # Business Goals
                if "business_goals" in website_info and website_info["business_goals"]:
                    st.markdown(f"<div class='emoji-header'>🎯 BUSINESS GOALS:</div>", unsafe_allow_html=True)
                    for goal in website_info["business_goals"]:
                        st.markdown(f"<p class='info-item'>• {goal}</p>", unsafe_allow_html=True)

                # Website URLs
                if "website" in website_info:
                    st.markdown(f"<div class='emoji-header'>🔗 WEBSITE:</div>", unsafe_allow_html=True)
                    st.markdown(f"<p class='info-item'>• Main: <a href='{website_info['website']['main']}' target='_blank'>{website_info['website']['main']}</a></p>", unsafe_allow_html=True)
                    if website_info['website']['about'] != "Not available":
                        st.markdown(f"<p class='info-item'>• About: <a href='{website_info['website']['about']}' target='_blank'>{website_info['website']['about']}</a></p>", unsafe_allow_html=True)

                # Social Media Links
                if "social_links" in website_info and website_info["social_links"]:
                    st.markdown(f"<div class='emoji-header'>📱 SOCIAL MEDIA:</div>", unsafe_allow_html=True)
                    for platform, url in website_info["social_links"].items():
                        st.markdown(f"<p class='info-item'>• {platform.capitalize()}: <a href='{url}' target='_blank'>{url}</a></p>", unsafe_allow_html=True)

                # About the Business
                if "about_the_business" in website_info and website_info["about_the_business"]:
                    st.markdown(f"<div class='emoji-header'>📋 ABOUT THE BUSINESS:</div>", unsafe_allow_html=True)
                    for paragraph in website_info["about_the_business"]:
                        st.markdown(f"<p class='info-item'>{paragraph}</p>", unsafe_allow_html=True)

                # Target Market
                if "target_market" in website_info and website_info["target_market"]:
                    st.markdown(f"<div class='emoji-header'>👥 TARGET MARKET:</div>", unsafe_allow_html=True)
                    for target in website_info["target_market"]:
                        st.markdown(f"<p class='info-item'>• {target}</p>", unsafe_allow_html=True)

                # Products/Services
                if "products_services" in website_info and website_info["products_services"]:
                    st.markdown(f"<div class='emoji-header'>🛍️ PRODUCTS/SERVICES:</div>", unsafe_allow_html=True)
                    for product in website_info["products_services"]:
                        st.markdown(f"<p class='info-item'>• {product}</p>", unsafe_allow_html=True)

                # Unique Value Proposition
                if "unique_value_proposition" in website_info and website_info["unique_value_proposition"]:
                    st.markdown(f"<div class='emoji-header'>💎 UNIQUE VALUE PROPOSITION:</div>", unsafe_allow_html=True)
                    for value in website_info["unique_value_proposition"]:
                        st.markdown(f"<p class='info-item'>• {value}</p>", unsafe_allow_html=True)
            else:
                # Add a note if no website information is available
                st.markdown("<hr style='margin: 30px 0; border-top: 1px solid #E0E0E0;'>", unsafe_allow_html=True)

                # Check if website_info is a dictionary with an error message
                if isinstance(st.session_state.website_info, dict) and "error" in st.session_state.website_info:
                    error_msg = st.session_state.website_info["error"]
                    st.markdown(f"<p style='color: #D32F2F;'>Error: {error_msg}</p>", unsafe_allow_html=True)
                else:
                    st.markdown("<p style='color: #757575;'>Detailed website information not available. Please run the analysis again.</p>", unsafe_allow_html=True)

            # Close the business-info-container div
            st.markdown("</div>", unsafe_allow_html=True)
        else:
            st.info("Business information not available. Please run the analysis again.")

    with results_tabs[1]:
        st.markdown("<div class='section-header'>Competitor Analysis Results</div>", unsafe_allow_html=True)

        # Create a DataFrame for better display
        competitor_data = []
        for comp in st.session_state.competitors:
            competitor_data.append({
                "Name": comp["name"],
                "LinkedIn": "Available" if comp["linkedin"] != "N/A" else "Not Found",
                "Instagram": "Available" if comp["instagram"] != "N/A" else "Not Found",
                "Facebook": "Available" if comp["facebook"] != "N/A" else "Not Found",
                "TikTok": "Available" if comp["tiktok"] != "N/A" else "Not Found"
            })

        df = pd.DataFrame(competitor_data)
        st.dataframe(df, use_container_width=True)

        # Display competitor details
        st.markdown("<div class='section-header'>Competitor Details</div>", unsafe_allow_html=True)

        for comp in st.session_state.competitors:
            with st.expander(f"{comp['name']} - Details", expanded=False):
                st.markdown(f"<div class='competitor-card'>", unsafe_allow_html=True)
                st.markdown(f"<b>Name:</b> {comp['name']}", unsafe_allow_html=True)

                # Social media links
                st.markdown("<b>Social Media Links:</b>", unsafe_allow_html=True)
                if comp["linkedin"] != "N/A":
                    st.markdown(f"- LinkedIn: <a href='{comp['linkedin']}' target='_blank'>{comp['linkedin']}</a>", unsafe_allow_html=True)
                else:
                    st.markdown("- LinkedIn: Not Available", unsafe_allow_html=True)

                if comp["instagram"] != "N/A":
                    st.markdown(f"- Instagram: <a href='{comp['instagram']}' target='_blank'>{comp['instagram']}</a>", unsafe_allow_html=True)
                else:
                    st.markdown("- Instagram: Not Available", unsafe_allow_html=True)

                if comp["facebook"] != "N/A":
                    st.markdown(f"- Facebook: <a href='{comp['facebook']}' target='_blank'>{comp['facebook']}</a>", unsafe_allow_html=True)
                else:
                    st.markdown("- Facebook: Not Available", unsafe_allow_html=True)

                if comp["tiktok"] != "N/A":
                    st.markdown(f"- TikTok: <a href='{comp['tiktok']}' target='_blank'>{comp['tiktok']}</a>", unsafe_allow_html=True)
                else:
                    st.markdown("- TikTok: Not Available", unsafe_allow_html=True)

                st.markdown("</div>", unsafe_allow_html=True)

    with results_tabs[2]:
        st.markdown("<div class='section-header'>Social Media Analysis</div>", unsafe_allow_html=True)

        # Select competitor for social media analysis
        competitor_names = [comp["name"] for comp in st.session_state.competitors]
        selected_competitor_name = st.selectbox("Select a competitor to view their social media posts", competitor_names)

        # Display social media posts for the selected competitor
        if selected_competitor_name in st.session_state.social_media_data:
            posts_data = st.session_state.social_media_data[selected_competitor_name]

            # Create tabs for different social media platforms
            social_tabs = st.tabs(["LinkedIn", "Instagram", "TikTok"])

            with social_tabs[0]:  # LinkedIn
                if "posts" in posts_data and "linkedin" in posts_data["posts"]:
                    linkedin_posts = posts_data["posts"]["linkedin"]
                    if isinstance(linkedin_posts, list) and linkedin_posts:
                        st.markdown("<div class='section-header'>LinkedIn Posts</div>", unsafe_allow_html=True)
                        st.markdown("<div class='post-container'>", unsafe_allow_html=True)
                        for i, post in enumerate(linkedin_posts):
                            st.markdown(f"<div class='social-card'><b>Post {i+1}:</b> <a href='{post}' target='_blank'>{post}</a></div>", unsafe_allow_html=True)
                        st.markdown("</div>", unsafe_allow_html=True)
                    else:
                        st.info("No LinkedIn posts found or unable to retrieve posts.")
                else:
                    st.info("No LinkedIn data available for this competitor.")

            with social_tabs[1]:  # Instagram
                if "posts" in posts_data and "instagram" in posts_data["posts"]:
                    instagram_posts = posts_data["posts"]["instagram"]
                    if isinstance(instagram_posts, list) and instagram_posts:
                        st.markdown("<div class='section-header'>Instagram Posts</div>", unsafe_allow_html=True)
                        st.markdown("<div class='post-container'>", unsafe_allow_html=True)
                        for i, post in enumerate(instagram_posts):
                            st.markdown(f"<div class='social-card'><b>Post {i+1}:</b> <a href='{post}' target='_blank'>{post}</a></div>", unsafe_allow_html=True)
                        st.markdown("</div>", unsafe_allow_html=True)
                    else:
                        st.info("No Instagram posts found or unable to retrieve posts.")
                else:
                    st.info("No Instagram data available for this competitor.")

            with social_tabs[2]:  # TikTok
                if "posts" in posts_data and "tiktok" in posts_data["posts"]:
                    tiktok_posts = posts_data["posts"]["tiktok"]
                    if isinstance(tiktok_posts, list) and tiktok_posts:
                        st.markdown("<div class='section-header'>TikTok Posts</div>", unsafe_allow_html=True)
                        st.markdown("<div class='post-container'>", unsafe_allow_html=True)
                        for i, post in enumerate(tiktok_posts):
                            st.markdown(f"<div class='social-card'><b>Post {i+1}:</b> <a href='{post}' target='_blank'>{post}</a></div>", unsafe_allow_html=True)
                        st.markdown("</div>", unsafe_allow_html=True)
                    else:
                        st.info("No TikTok posts found or unable to retrieve posts.")
                else:
                    st.info("No TikTok data available for this competitor.")
        else:
            st.info("No social media data available for this competitor yet.")

# Sidebar with information
with st.sidebar:
    st.markdown("<div class='section-header'>How to Use</div>", unsafe_allow_html=True)
    st.markdown("""
    <div class='info-text'>
    <b>Step 1:</b> Enter your business website URL.<br><br>
    <b>Step 2:</b> Click "Analyze Business & Competitors" to automatically:<br>
    &nbsp;&nbsp;&nbsp;&nbsp;• Extract your business domain and goal<br>
    &nbsp;&nbsp;&nbsp;&nbsp;• Identify competitors in your industry<br>
    &nbsp;&nbsp;&nbsp;&nbsp;• Collect their social media posts<br><br>
    <b>Step 3:</b> View your business information in the "Business Information" tab.<br><br>
    <b>Step 4:</b> Explore the "Competitor Overview" tab to see competitor details.<br><br>
    <b>Step 5:</b> Switch to the "Social Media Analysis" tab to view social media posts and comments for each competitor.
    </div>
    """, unsafe_allow_html=True)

    st.markdown("<div class='section-header'>About</div>", unsafe_allow_html=True)
    st.markdown("""
    <div class='info-text'>
    Deylegen helps businesses analyze competitors and monitor their social media activity to gain insights and generate leads. Just enter your website URL and let AI do the rest!
    </div>
    """, unsafe_allow_html=True)

# Run the app with: streamlit run app.py
