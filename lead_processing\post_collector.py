import re
import os
import sys
import requests
from dotenv import load_dotenv
from typing import Dict, List
from rich import print

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
load_dotenv()

class Tiktok:
    """
    A class to collect and analyze TikTok posts and profile information.

    This class uses the RapidAPI TikTok API to fetch posts and profile information
    from TikTok accounts. It handles URL parsing, API requests, and post extraction.

    Attributes:
        api_key (str): RapidAPI key for TikTok API access
        api_host (str): RapidAPI host for TikTok API endpoints
        base_url (str): Base URL for API requests
        headers (dict): HTTP headers for API requests
    """
    def __init__(self):
        """
        Initialize the TikTok collector with RapidAPI credentials.
        """
        # Use the provided API key and host
        self.api_key = os.getenv("TIKTOK_RAPID_API_KEY", "c5dab30c19mshe65df2c159b3ceap1c15ffjsnc8f624885e94")
        self.api_host = os.getenv("TIKTOK_RAPID_API_HOST", "tiktok-api6.p.rapidapi.com")
        self.base_url = f"https://{self.api_host}"
        self.headers = {
            "x-rapidapi-key": self.api_key,
            "x-rapidapi-host": self.api_host,
        }

    def _make_request(self, path: str, params: dict) -> dict:
        """
        Make a request to the TikTok API.

        Args:
            path: API endpoint path
            params: Query parameters

        Returns:
            API response as a dictionary
        """
        url = f"{self.base_url}{path}"

        try:
            # Make the API request
            response = requests.get(url, headers=self.headers, params=params, timeout=15)

            # Raise an exception for 4XX and 5XX status codes
            response.raise_for_status()

            # Parse and return the JSON response
            return response.json()
        except Exception:
            return {}

    def get_profile_posts(self, url: str, count: int = 20) -> list[str]:
        """
        Fetches post URLs from a TikTok user's profile using the API.

        Args:
            url: TikTok URL or username
            count: Number of posts to retrieve

        Returns:
            List of TikTok post URLs
        """
        # Extract username if a URL is provided
        username = url
        if "tiktok.com" in url:
            try:
                username = self.extract_username_from_url(url)
                if __name__ == "__main__":
                    print(f"Extracted username from URL: {username}")
            except ValueError as e:
                # If we can't extract a username, just use the original value
                if __name__ == "__main__":
                    print(f"Could not extract username from URL: {e}")

                # Try a simpler approach - extract the part after tiktok.com/
                parts = url.split('tiktok.com/')
                if len(parts) > 1:
                    # Get everything after tiktok.com/
                    potential_username = parts[1]

                    # Remove @ if present
                    if potential_username.startswith('@'):
                        potential_username = potential_username[1:]

                    # Get the first part before any slash
                    if '/' in potential_username:
                        potential_username = potential_username.split('/')[0]

                    # If we have something, use it
                    if potential_username:
                        username = potential_username
                        if __name__ == "__main__":
                            print(f"Using alternative username extraction: {username}")

        # Use the user/videos endpoint which has been tested and works
        endpoints = [
            # The user/videos endpoint works reliably
            {
                "path": "/user/videos",
                "params": {"username": username, "count": str(count)}
            }
        ]

        for endpoint in endpoints:
            try:
                path = endpoint["path"]
                params = endpoint["params"]

                # Make the API request
                response = self._make_request(path, params)

                # Extract posts from the response
                posts = []
                if response:
                    if "data" in response:
                        posts = response.get("data", [])
                    elif isinstance(response, list):
                        posts = response
                    elif "videos" in response:
                        posts = response.get("videos", [])
                    elif "items" in response:
                        posts = response.get("items", [])

                # Extract post URLs from the response
                post_urls = []
                for post in posts:
                    # Get the video ID
                    video_id = None
                    username = username  # Use the extracted username

                    # Try different field names for video ID
                    for field in ["id", "video_id", "aweme_id", "item_id", "videoId"]:
                        if isinstance(post, dict) and field in post:
                            video_id = post[field]
                            break

                    # Try to extract username from the post if available
                    if isinstance(post, dict) and "author" in post:
                        author = post["author"]
                        if isinstance(author, dict):
                            for field in ["username", "unique_id", "uniqueId", "nickname"]:
                                if field in author:
                                    username = author[field]
                                    break

                    if video_id:
                        # Construct the post URL
                        post_url = self.construct_post_url(username, video_id)
                        post_urls.append(post_url)

                if post_urls:
                    return post_urls
            except Exception:
                continue

        # If all endpoints failed, return an empty list
        return []


    def extract_username_from_url(self, url: str) -> str:
        """
        Extract the username from a TikTok URL.
        Example: https://www.tiktok.com/@username/video/1234567890123456789
        """
        m = re.search(r'tiktok\.com/@([^/]+)', url)
        if not m:
            raise ValueError(f"Could not extract username from URL: {url}")
        return m.group(1)

    def extract_video_id_from_url(self, url: str) -> str:
        """
        Extract the video ID from a TikTok URL.
        Example: https://www.tiktok.com/@username/video/1234567890123456789
        """
        m = re.search(r'/video/(\d+)', url)
        if not m:
            raise ValueError(f"Could not extract video ID from URL: {url}")
        return m.group(1)

    def construct_post_url(self, username: str, video_id: str) -> str:
        """
        Construct a TikTok post URL from a username and video ID.
        """
        return f"https://www.tiktok.com/@{username}/video/{video_id}"

    def run(self, url, count=5):
        all_posts = []

        posts = self.get_profile_posts(url, count=count)

        if posts:
            # print(f"\nPosts for {url}:")  # Comment out to prevent terminal output
            for i, post in enumerate(posts, 1):
                # print(f"{i}. {post}")  # Comment out to prevent terminal output
                all_posts.append(post)  # Collect each post into the list

        return all_posts  # Return the collected posts


class LinkedInCollector:
    """
    A class to collect and analyze LinkedIn company posts and profile information.

    This class uses the RapidAPI LinkedIn API to fetch posts and company information
    from LinkedIn company pages. It handles URL parsing, API requests, and post extraction.

    Attributes:
        rapidapi_key (str): RapidAPI key for LinkedIn API access
        headers (dict): HTTP headers for API requests containing RapidAPI credentials
    """
    def __init__(self):
        self.rapidapi_key = os.getenv('LINKEDIN_RAPID_API_KEY') or "c5dab30c19mshe65df2c159b3ceap1c15ffjsnc8f624885e94"
        self.headers = {
            "x-rapidapi-key": self.rapidapi_key,
            "x-rapidapi-host": "linkedin-data-api.p.rapidapi.com"
        }



    def get_company_posts(self, url: str):
        """
        Get all post URLs from a LinkedIn company page.

        Args:
            url: LinkedIn company URL or name

        Returns:
            A list of post URLs
        """
        try:
            # Extract company name from URL if it's a URL
            company_name = url

            # Check if it's a LinkedIn URL
            if "linkedin.com" in url:
                # Extract company name from URL like https://www.linkedin.com/company/company-name
                if "company/" in url:
                    parts = url.split("company/")
                    if len(parts) > 1:
                        company_name = parts[1]
                        # Remove any trailing parameters or slashes
                        if '?' in company_name:
                            company_name = company_name.split('?')[0]
                        if '/' in company_name:
                            company_name = company_name.split('/')[0]

                        # print(f"Extracted company name from URL: {company_name}")  # Comment out to prevent terminal output

            api_url = "https://linkedin-data-api.p.rapidapi.com/get-company-posts"

            querystring = {"username": company_name, "start": "0"}

            # print(f"Fetching posts for LinkedIn company: {url}")  # Comment out to prevent terminal output
            response = requests.get(api_url, headers=self.headers, params=querystring, timeout=15)

            # Check if the request was successful
            if response.status_code == 200:
                company_posts = []

                # Parse the JSON response
                response_data = response.json()

                # Get the data array from the response, or an empty list if it doesn't exist
                datas = response_data.get('data', [])

                # Extract post URLs from the data
                for data in datas:
                    if isinstance(data, dict) and 'postUrl' in data:
                        company_posts.append(data['postUrl'])

                return company_posts
            else:
                # print(f"API request failed with status code: {response.status_code}")  # Comment out to prevent terminal output
                # if hasattr(response, 'text'):
                #     print(f"Response: {response.text}")  # Comment out to prevent terminal output
                return []

        except Exception as e:
            # print(f"Error getting company posts: {e}")  # Comment out to prevent terminal output
            return []

    def run(self,url):


        company_posts = self.get_company_posts(url)

        all_posts  = []
        if company_posts:
            for i, post_url in enumerate(company_posts, 1):

                all_posts.append(post_url)

            return  all_posts

        else:
            return f"No posts found for {url} or an error occurred."

class Instagram:
    """
    A class to collect and analyze Instagram posts and profile information.

    This class uses the RapidAPI Instagram API to fetch posts and profile information
    from Instagram accounts. It handles URL parsing, API requests, and post extraction.

    Attributes:
        api_key (str): RapidAPI key for Instagram API access
        post_details_api_host (str): API host for post details endpoints
        user_posts_api_host (str): API host for user posts endpoints
        post_details_headers (dict): HTTP headers for post details requests
        user_posts_headers (dict): HTTP headers for user posts requests
    """
    def __init__(self):
        self.api_key = os.getenv('INSTAGRAM_RAPID_API_KEY') or "c5dab30c19mshe65df2c159b3ceap1c15ffjsnc8f624885e94"
        # Use different API hosts for different endpoints
        self.post_details_api_host = "instagram-scraper-stable-api.p.rapidapi.com"
        self.user_posts_api_host = "instagram230.p.rapidapi.com"

        # Default headers for post details
        self.post_details_headers = {
            'x-rapidapi-key': self.api_key,
            'x-rapidapi-host': self.post_details_api_host
        }

        # Headers for user posts
        self.user_posts_headers = {
            'x-rapidapi-key': self.api_key,
            'x-rapidapi-host': self.user_posts_api_host
        }

    def extract_shortcode_from_url(self, url: str) -> str:
        """Extract shortcode from Instagram URL"""
        shortcode_pattern = r'instagram\.com/p/([A-Za-z0-9_-]+)'
        shortcode_match = re.search(shortcode_pattern, url)

        if shortcode_match:
            return shortcode_match.group(1)
        raise ValueError(f"Could not extract shortcode from URL: {url}")


    def get_profile_posts(self, url: str, count: int = 20) -> List[str]:
        """
        Get all post URLs for a profile.

        Args:
            url: Instagram URL or username
            count: Number of posts to retrieve (default: 20)

        Returns:
            List of Instagram post URLs
        """
        try:
            # Extract username from URL if it's a URL, or use as is
            username = url

            # If it's an Instagram URL, extract the username
            if "instagram.com" in url:
                # Extract username from URL like https://www.instagram.com/username/
                match = re.search(r'instagram\.com/([^/]+)', url)
                if match:
                    username = match.group(1)
                    # Remove any trailing parameters
                    if '?' in username:
                        username = username.split('?')[0]
                    # Remove trailing slash if present
                    if username.endswith('/'):
                        username = username[:-1]

                    if __name__ == "__main__":
                        print(f"Extracted username from URL: {username}")

            # Remove @ symbol if present in the username
            if username.startswith('@'):
                username = username[1:]

            # Make an API request to get user posts using the provided endpoint
            api_url = f"https://{self.user_posts_api_host}/user/posts"
            querystring = {"username": username}
            if count:
                querystring["limit"] = str(count)

            # Make the API request
            response = requests.get(
                api_url,
                headers=self.user_posts_headers,
                params=querystring,
                timeout=15
            )

            # If we get a successful response, process it
            if response.status_code == 200:
                data = response.json()
                post_urls = []

                # Extract posts from the response
                posts = []
                if isinstance(data, list):
                    posts = data
                elif isinstance(data, dict):
                    if 'data' in data and isinstance(data['data'], list):
                        posts = data['data']
                    elif 'posts' in data and isinstance(data['posts'], list):
                        posts = data['posts']
                    elif 'items' in data and isinstance(data['items'], list):
                        posts = data['items']
                    elif 'result' in data and isinstance(data['result'], list):
                        posts = data['result']

                # Extract post URLs from the posts
                for post in posts:
                    if isinstance(post, dict):
                        # Try to get the post URL directly
                        post_url = None
                        if 'post_url' in post and post['post_url']:
                            post_url = post['post_url']
                        elif 'url' in post and post['url']:
                            post_url = post['url']

                        # If we couldn't get the URL directly, try to construct it from the shortcode
                        if not post_url:
                            shortcode = None
                            for field in ['shortcode', 'code', 'id', 'post_id', 'postId']:
                                if field in post and post[field]:
                                    shortcode = post[field]
                                    break

                            if shortcode:
                                post_url = f"https://www.instagram.com/p/{shortcode}/"

                        if post_url:
                            post_urls.append(post_url)

                # Limit the number of posts if needed
                if len(post_urls) > count:
                    post_urls = post_urls[:count]

                return post_urls

            # If the API request fails, return an empty list
            return []

        except Exception:
            # Return an empty list in case of error
            return []

    def run(self,url,count = 10 ):


         # Number of posts to retrieve
        profile_posts = self.get_profile_posts(url, count)
        all_posts = []
        if profile_posts:
            for i, post_url in enumerate(profile_posts, 1):
                all_posts.append(post_url)
            return all_posts
        else:
            return f"No posts found for {url} or an error occurred."


        # Initialize the collector


# Example usage
if __name__ == "__main__":
    #
    # tictok = Tiktok()
    #
    # # Get post URLs for a TikTok user (can use username or URL)
    # url = "https://www.tiktok.com/@johndeere"
    # url  = tictok.run(url)
    #
    # print(url)


    linkedin  =  LinkedInCollector()
    url  = " https://www.linkedin.com/company/john-deere"  # Can also be just the company name: "amazon"
    url  = linkedin.run(url)
    # print(url)  # Comment out to prevent terminal output

    instagram  = Instagram()
    profile_url = "https://www.instagram.com/johndeere/"
    url = instagram.run(profile_url)

    # print(url)  # Comment out to prevent terminal output