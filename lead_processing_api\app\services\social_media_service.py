"""
Social Media Service for Lead Processing API.

Handles all social media-related functionality including:
- Social media post collection
- Platform-specific scraping
- Content analysis
- Multi-platform aggregation
"""

import os
import sys
from typing import Dict, Any, List, Optional

# Add the lead_processing directory to the path
lead_processing_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', 'lead_processing'))
if lead_processing_path not in sys.path:
    sys.path.insert(0, lead_processing_path)

# Import the social media modules
post_collector = None
lead_main = None

try:
    import post_collector
    print("✅ Social Media Service: Successfully imported post_collector")
except ImportError as e:
    print(f"⚠️  Social Media Service: Could not import post_collector: {e}")

try:
    import lead_processing.main as lead_main
    print("✅ Social Media Service: Successfully imported lead_processing main")
except ImportError as e:
    print(f"⚠️  Social Media Service: Could not import lead_processing main: {e}")


class SocialMediaService:
    """Service for social media post collection and analysis."""

    def __init__(self):
        """Initialize social media service."""
        self.is_available = post_collector is not None and lead_main is not None

        if not self.is_available:
            print("⚠️  Social media service not available - required modules not imported")

    def collect_posts_by_platform(
        self,
        platform: str,
        profile_url: str,
        limit: int = 10
    ) -> Dict[str, Any]:
        """
        Collect posts from a specific social media platform.

        Args:
            platform: Social media platform (linkedin, instagram, facebook, tiktok)
            profile_url: URL of the profile/page
            limit: Maximum number of posts to collect

        Returns:
            Dict containing collected posts
        """
        if not self.is_available:
            return {
                "error": "Social media service not available",
                "platform": platform,
                "posts": [],
                "posts_count": 0
            }

        try:
            # Validate platform
            supported_platforms = ["linkedin", "instagram", "facebook", "tiktok"]
            if platform.lower() not in supported_platforms:
                return {
                    "error": f"Unsupported platform: {platform}",
                    "platform": platform,
                    "posts": [],
                    "posts_count": 0,
                    "supported_platforms": supported_platforms
                }

            # Use the post collector to gather posts
            posts = []

            if platform.lower() == "linkedin":
                linkedin_collector = post_collector.LinkedInCollector()
                posts = linkedin_collector.run(profile_url)
            elif platform.lower() == "instagram":
                instagram_collector = post_collector.Instagram()
                posts = instagram_collector.run(profile_url, count=limit)
            elif platform.lower() == "tiktok":
                tiktok_collector = post_collector.Tiktok()
                posts = tiktok_collector.run(profile_url, count=limit)
            elif platform.lower() == "facebook":
                # Facebook scraping not implemented yet
                posts = []
            else:
                # Fallback to general collection method
                posts = self._collect_posts_generic(platform, profile_url, limit)

            # Ensure posts is a list
            if not isinstance(posts, list):
                posts = []

            return {
                "platform": platform,
                "profile_url": profile_url,
                "posts": posts,
                "posts_count": len(posts),
                "limit": limit,
                "error": None
            }

        except Exception as e:
            return {
                "error": f"Post collection failed for {platform}: {str(e)}",
                "platform": platform,
                "profile_url": profile_url,
                "posts": [],
                "posts_count": 0
            }

    def collect_competitor_posts(self, competitor_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Collect posts from all social media platforms for a competitor.

        Args:
            competitor_data: Dict containing competitor info with social links

        Returns:
            Dict containing posts from all platforms
        """
        if not self.is_available:
            return {
                "error": "Social media service not available",
                "competitor": competitor_data.get("name", "Unknown"),
                "posts": {},
                "total_posts": 0
            }

        try:
            # Use the existing lead_main functionality
            posts_data = lead_main.collect_social_media_posts(competitor_data)

            # Process and normalize the results
            all_posts = {}
            total_posts = 0

            if 'posts' in posts_data:
                for platform, posts in posts_data['posts'].items():
                    if posts and isinstance(posts, list):
                        all_posts[platform] = posts
                        total_posts += len(posts)
                    else:
                        all_posts[platform] = []

            return {
                "competitor": competitor_data.get("name", "Unknown"),
                "posts": all_posts,
                "total_posts": total_posts,
                "platforms_checked": list(all_posts.keys()),
                "error": None
            }

        except Exception as e:
            return {
                "error": f"Competitor post collection failed: {str(e)}",
                "competitor": competitor_data.get("name", "Unknown"),
                "posts": {},
                "total_posts": 0
            }

    def collect_linkedin_posts(self, profile_url: str, limit: int = 10) -> Dict[str, Any]:
        """
        Collect posts specifically from LinkedIn.

        Args:
            profile_url: LinkedIn profile/company URL
            limit: Maximum number of posts to collect

        Returns:
            Dict containing LinkedIn posts
        """
        return self.collect_posts_by_platform("linkedin", profile_url, limit)

    def collect_instagram_posts(self, profile_url: str, limit: int = 10) -> Dict[str, Any]:
        """
        Collect posts specifically from Instagram.

        Args:
            profile_url: Instagram profile URL
            limit: Maximum number of posts to collect

        Returns:
            Dict containing Instagram posts
        """
        return self.collect_posts_by_platform("instagram", profile_url, limit)

    def collect_facebook_posts(self, profile_url: str, limit: int = 10) -> Dict[str, Any]:
        """
        Collect posts specifically from Facebook.

        Args:
            profile_url: Facebook page URL
            limit: Maximum number of posts to collect

        Returns:
            Dict containing Facebook posts
        """
        return self.collect_posts_by_platform("facebook", profile_url, limit)

    def collect_tiktok_posts(self, profile_url: str, limit: int = 10) -> Dict[str, Any]:
        """
        Collect posts specifically from TikTok.

        Args:
            profile_url: TikTok profile URL
            limit: Maximum number of posts to collect

        Returns:
            Dict containing TikTok posts
        """
        return self.collect_posts_by_platform("tiktok", profile_url, limit)

    def analyze_post_content(self, posts: List[str]) -> Dict[str, Any]:
        """
        Analyze content from collected posts.

        Args:
            posts: List of post URLs or content

        Returns:
            Dict containing content analysis
        """
        try:
            # Basic content analysis
            total_posts = len(posts)

            # Categorize by platform (based on URL patterns)
            platform_counts = {
                "linkedin": len([p for p in posts if "linkedin.com" in str(p)]),
                "instagram": len([p for p in posts if "instagram.com" in str(p)]),
                "facebook": len([p for p in posts if "facebook.com" in str(p)]),
                "tiktok": len([p for p in posts if "tiktok.com" in str(p)]),
                "other": 0
            }

            platform_counts["other"] = total_posts - sum(platform_counts.values())

            return {
                "total_posts": total_posts,
                "platform_distribution": platform_counts,
                "analysis_date": "2024-01-01",  # You could use datetime.now()
                "most_active_platform": max(platform_counts, key=platform_counts.get),
                "error": None
            }

        except Exception as e:
            return {
                "error": f"Content analysis failed: {str(e)}",
                "total_posts": 0,
                "platform_distribution": {},
                "analysis_date": None,
                "most_active_platform": None
            }

    def _collect_posts_generic(
        self,
        platform: str,
        profile_url: str,
        limit: int
    ) -> List[str]:
        """
        Generic post collection method as fallback.

        Args:
            platform: Social media platform
            profile_url: Profile URL
            limit: Post limit

        Returns:
            List of post URLs/content
        """
        try:
            # This is a placeholder implementation
            # In a real scenario, you'd implement platform-specific scraping

            # For now, return empty list to indicate no posts collected
            # but service is working
            return []

        except Exception as e:
            print(f"Generic post collection failed for {platform}: {e}")
            return []

    def get_supported_platforms(self) -> List[str]:
        """
        Get list of supported social media platforms.

        Returns:
            List of supported platform names
        """
        return ["linkedin", "instagram", "facebook", "tiktok"]

    def is_service_available(self) -> bool:
        """
        Check if the social media service is available.

        Returns:
            bool: True if service is available, False otherwise
        """
        return self.is_available

    def get_service_status(self) -> Dict[str, Any]:
        """
        Get detailed service status information.

        Returns:
            Dict containing service status
        """
        return {
            "service": "social_media_service",
            "available": self.is_available,
            "supported_platforms": self.get_supported_platforms(),
            "capabilities": [
                "post_collection",
                "multi_platform_aggregation",
                "content_analysis",
                "competitor_social_analysis"
            ] if self.is_available else [],
            "dependencies": {
                "post_collector": post_collector is not None,
                "lead_main": lead_main is not None
            }
        }
