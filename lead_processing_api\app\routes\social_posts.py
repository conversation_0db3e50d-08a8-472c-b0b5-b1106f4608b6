from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
import sys
import os

# Add the lead_processing directory to the path
lead_processing_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'lead_processing'))
if lead_processing_path not in sys.path:
    sys.path.insert(0, lead_processing_path)

# Import the modules
post_collector = None
lead_main = None

try:
    import post_collector
    print("✅ Social posts route: Successfully imported post_collector")
except ImportError as e:
    print(f"⚠️  Social posts route: Could not import post_collector: {e}")

try:
    import lead_processing.main as lead_main
    print("✅ Social posts route: Successfully imported lead_processing main")
except ImportError as e:
    print(f"⚠️  Social posts route: Could not import lead_processing main: {e}")

router = APIRouter(
    prefix="/social-posts",
    tags=["social-posts"],
)

# Pydantic models for social media posts
class SocialMediaRequest(BaseModel):
    """Request model for social media post collection."""
    platform: str
    url: str
    count: Optional[int] = 10

    class Config:
        json_schema_extra = {
            "example": {
                "platform": "linkedin",
                "url": "https://www.linkedin.com/company/example-company",
                "count": 10
            }
        }

class CompetitorPostsRequest(BaseModel):
    """Request model for collecting posts from a competitor."""
    name: str
    linkedin: Optional[str] = "N/A"
    instagram: Optional[str] = "N/A"
    facebook: Optional[str] = "N/A"
    tiktok: Optional[str] = "N/A"

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Example Company",
                "linkedin": "https://www.linkedin.com/company/example-company",
                "instagram": "https://www.instagram.com/examplecompany/",
                "facebook": "https://www.facebook.com/ExampleCompany",
                "tiktok": "N/A"
            }
        }

class SocialPostsResponse(BaseModel):
    """Response model for social media posts."""
    platform: str
    url: str
    posts: List[str]
    total_posts: int

    class Config:
        json_schema_extra = {
            "example": {
                "platform": "linkedin",
                "url": "https://www.linkedin.com/company/example-company",
                "posts": [
                    "https://www.linkedin.com/posts/example-company_post1",
                    "https://www.linkedin.com/posts/example-company_post2"
                ],
                "total_posts": 2
            }
        }

class CompetitorPostsResponse(BaseModel):
    """Response model for competitor posts across all platforms."""
    name: str
    posts: Dict[str, List[str]]
    total_posts_collected: int

    class Config:
        json_schema_extra = {
            "example": {
                "name": "Example Company",
                "posts": {
                    "linkedin": ["https://linkedin.com/posts/post1"],
                    "instagram": ["https://instagram.com/p/post1"],
                    "facebook": [],
                    "tiktok": []
                },
                "total_posts_collected": 2
            }
        }

@router.post("/linkedin", response_model=SocialPostsResponse)
def collect_linkedin_posts(request: SocialMediaRequest):
    """
    Collect posts from a LinkedIn company page.

    **Parameters:**
    - **platform**: Should be "linkedin"
    - **url**: LinkedIn company URL or company name
    - **count**: Number of posts to collect (default: 10)

    **Returns:**
    - List of LinkedIn post URLs
    """
    if post_collector is None:
        raise HTTPException(
            status_code=500,
            detail="Post collector module not available"
        )

    try:
        linkedin_collector = post_collector.LinkedInCollector()
        posts = linkedin_collector.run(request.url)

        # Handle the case where posts might be an error message
        if isinstance(posts, str) and "No posts found" in posts:
            posts = []
        elif not isinstance(posts, list):
            posts = []

        response = SocialPostsResponse(
            platform="linkedin",
            url=request.url,
            posts=posts,
            total_posts=len(posts)
        )

        return response

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"LinkedIn post collection failed: {str(e)}"
        )

@router.post("/instagram", response_model=SocialPostsResponse)
def collect_instagram_posts(request: SocialMediaRequest):
    """
    Collect posts from an Instagram profile.

    **Parameters:**
    - **platform**: Should be "instagram"
    - **url**: Instagram profile URL or username
    - **count**: Number of posts to collect (default: 10)

    **Returns:**
    - List of Instagram post URLs
    """
    if post_collector is None:
        raise HTTPException(
            status_code=500,
            detail="Post collector module not available"
        )

    try:
        instagram_collector = post_collector.Instagram()
        posts = instagram_collector.run(request.url, request.count)

        # Handle the case where posts might be an error message
        if isinstance(posts, str) and "No posts found" in posts:
            posts = []
        elif not isinstance(posts, list):
            posts = []

        response = SocialPostsResponse(
            platform="instagram",
            url=request.url,
            posts=posts,
            total_posts=len(posts)
        )

        return response

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Instagram post collection failed: {str(e)}"
        )

@router.post("/tiktok", response_model=SocialPostsResponse)
def collect_tiktok_posts(request: SocialMediaRequest):
    """
    Collect posts from a TikTok profile.

    **Parameters:**
    - **platform**: Should be "tiktok"
    - **url**: TikTok profile URL or username
    - **count**: Number of posts to collect (default: 10)

    **Returns:**
    - List of TikTok post URLs
    """
    if post_collector is None:
        raise HTTPException(
            status_code=500,
            detail="Post collector module not available"
        )

    try:
        tiktok_collector = post_collector.Tiktok()
        posts = tiktok_collector.run(request.url, request.count)

        # Handle the case where posts might be an error message
        if isinstance(posts, str) and "No posts found" in posts:
            posts = []
        elif not isinstance(posts, list):
            posts = []

        response = SocialPostsResponse(
            platform="tiktok",
            url=request.url,
            posts=posts,
            total_posts=len(posts)
        )

        return response

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"TikTok post collection failed: {str(e)}"
        )

@router.post("/competitor", response_model=CompetitorPostsResponse)
def collect_competitor_posts(request: CompetitorPostsRequest):
    """
    Collect posts from all social media platforms for a competitor.

    This endpoint uses the main lead processing logic to collect posts
    from LinkedIn, Instagram, Facebook, and TikTok for a single competitor.

    **Parameters:**
    - **name**: Competitor name
    - **linkedin**: LinkedIn company URL (optional)
    - **instagram**: Instagram profile URL (optional)
    - **facebook**: Facebook page URL (optional)
    - **tiktok**: TikTok profile URL (optional)

    **Returns:**
    - Posts collected from all available platforms
    """
    if lead_main is None:
        raise HTTPException(
            status_code=500,
            detail="Lead processing main module not available"
        )

    try:
        # Create competitor dictionary in the format expected by the main module
        competitor = {
            'name': request.name,
            'linkedin': request.linkedin or 'N/A',
            'instagram': request.instagram or 'N/A',
            'facebook': request.facebook or 'N/A',
            'tiktok': request.tiktok or 'N/A'
        }

        # Use the existing collect_social_media_posts function
        result = lead_main.collect_social_media_posts(competitor)

        # Extract posts from the result
        posts = result.get('posts', {})

        # Clean up posts - convert error strings to empty lists
        cleaned_posts = {}
        for platform, platform_posts in posts.items():
            if isinstance(platform_posts, str) and "No posts found" in platform_posts:
                cleaned_posts[platform] = []
            elif isinstance(platform_posts, list):
                cleaned_posts[platform] = platform_posts
            else:
                cleaned_posts[platform] = []

        # Count total posts
        total_posts = 0
        for platform_posts in cleaned_posts.values():
            if isinstance(platform_posts, list):
                total_posts += len(platform_posts)

        response = CompetitorPostsResponse(
            name=request.name,
            posts=cleaned_posts,
            total_posts_collected=total_posts
        )

        return response

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Competitor post collection failed: {str(e)}"
        )

@router.get("/health")
def social_posts_health_check():
    """Health check for social media post collection functionality."""
    status = {
        "post_collector": post_collector is not None,
        "lead_main": lead_main is not None
    }

    if all(status.values()):
        return {"status": "available", "message": "Social media post collection ready", "modules": status}
    else:
        return {"status": "partial", "message": "Some modules not loaded", "modules": status}
