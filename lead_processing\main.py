import return_competitors
import post_collector

def collect_social_media_posts(competitor):
    """
    Collect posts from all social media platforms for a given competitor.

    Args:
        competitor (dict): Dictionary containing competitor information with social media links

    Returns:
        dict: Dictionary containing competitor name and their posts from each platform
    """
    result = {
        'name': competitor['name'],
        'posts': {}
    }

    # Collect LinkedIn posts
    if competitor['linkedin'] != 'N/A':
        linkedin = post_collector.LinkedInCollector()
        result['posts']['linkedin'] = linkedin.run(competitor['linkedin'])

    # Collect Instagram posts
    if competitor['instagram'] != 'N/A':
        instagram = post_collector.Instagram()
        result['posts']['instagram'] = instagram.run(competitor['instagram'])

    # Collect TikTok posts
    if competitor['tiktok'] != 'N/A':
        tiktok = post_collector.Tiktok()
        result['posts']['tiktok'] = tiktok.run(competitor['tiktok'])

    return result

def main():
    """
    Main function to demonstrate the usage of CompetitorAnalyzer and collect social media posts.
    """
    # Get competitor analysis
    analyzer = return_competitors.CompetitorAnalyzer()
    business_goal = "to be one of the best agriculture"
    url = 'dalensai.com'
    domain = "agriculture"
    formatted_output = analyzer.analyze_and_format(url, domain, business_goal)
    # print(formatted_output)  # Comment out to prevent terminal output


if __name__ == "__main__":
    main()