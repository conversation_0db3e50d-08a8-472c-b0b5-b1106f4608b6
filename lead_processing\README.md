# Lead GenBot - Competitor Analysis & Social Media Monitoring

A Streamlit application that helps businesses analyze competitors and monitor their social media activity to gain insights and generate leads.

## Features

- **Ultra-Simple Interface**: Just enter your website URL and let AI do the rest.
- **AI-Powered Business Analysis**: Automatically extracts your business domain and goals using OpenAI.
- **Automated Competitor Analysis**: Identifies and analyzes competitors in your industry without any additional input.
- **Integrated Social Media Monitoring**: Automatically collects and displays social media posts from competitors across LinkedIn, Instagram, and TikTok.
- **One-Click Analysis**: Complete business analysis and social media monitoring with a single click.

## Requirements

- Python 3.7+
- Streamlit
- OpenAI API key
- RapidAPI keys for LinkedIn, Instagram, and TikTok APIs

## Installation

1. Clone the repository or download the source code.
2. Install the required dependencies:

```bash
pip install streamlit pandas openai python-dotenv requests beautifulsoup4 rich
```

3. Create a `.env` file in the root directory with the following API keys:

```
OPENAI_API_KEY=your_openai_api_key
LINKEDIN_RAPID_API_KEY=your_linkedin_api_key
INSTAGRAM_RAPID_API_KEY=your_instagram_api_key
TIKTOK_RAPID_API_KEY=your_tiktok_api_key
```

## Usage

1. Navigate to the `lead_processing` directory:

```bash
cd Lead-Genbot/lead_processing
```

2. Run the Streamlit app:

```bash
streamlit run app.py
```

3. Open your web browser and go to `http://localhost:8501` to access the application.

## How to Use

1. **Enter Your Website URL**:
   - Simply enter your business website URL (e.g., example.com).
   - Click "Analyze Business & Competitors" to start the automated analysis process.

2. **Let AI Do the Work**:
   - The app automatically extracts your business domain and goals.
   - It identifies competitors in your industry.
   - It collects social media posts from all competitors.

3. **View Results**:
   - View competitor information in the "Competitor Overview" tab.
   - Switch to the "Social Media Analysis" tab to browse through social media posts for each competitor.

## API Information

This application uses the following APIs:

- **OpenAI API**: For analyzing business websites and generating competitor insights.
- **LinkedIn API**: For collecting LinkedIn company posts (via RapidAPI).
- **Instagram API**: For collecting Instagram posts and comments (via RapidAPI).
- **TikTok API**: For collecting TikTok videos and profile information (via RapidAPI).

## File Structure

- `app.py`: The main Streamlit application file.
- `main.py`: Contains the core functionality for collecting social media posts.
- `return_competitors.py`: Handles competitor analysis using OpenAI.
- `post_collector.py`: Contains classes for collecting posts from different social media platforms.
- `website.py`: Handles website scraping and content analysis.

## Troubleshooting

- **API Key Issues**: Ensure all API keys in the `.env` file are valid and have sufficient credits.
- **Rate Limiting**: If you encounter rate limiting errors, wait a few minutes before trying again.
- **Website Scraping Errors**: Some websites may block scraping attempts. Try using a different URL or domain.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
